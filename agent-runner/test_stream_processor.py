#!/usr/bin/env python3
"""测试流式处理器的功能"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.agents.stream_processor import stream_handler, TextStreamProcessor, DataStreamProcessor
from app.models.schemas import StreamData


def test_research_node_processing():
    """测试 research 节点的数据处理"""
    print("=== 测试 Research 节点处理 ===")
    
    # 模拟 research 节点的 chunk 数据
    research_chunk = {
        "current_step": "research",
        "data": {
            "demand_info": "张三的密接人员信息",
            "final_result": "根据查询结果，张三的密接人员包括：李四、王五、赵六"
        },
        "stream": True
    }
    
    # 处理数据
    result = stream_handler.process_node_chunk(
        node_name="research",
        chunk=research_chunk,
        thread_id="test-thread-123"
    )
    
    if result:
        print(f"✅ Research 节点处理成功:")
        print(f"   节点: {result.node}")
        print(f"   状态: {result.status}")
        print(f"   流式类型: {result.stream_type}")
        print(f"   数据: {result.data}")
        print(f"   demand_info: {getattr(result, 'demand_info', 'N/A')}")
        print(f"   final_result: {getattr(result, 'final_result', 'N/A')}")
    else:
        print("❌ Research 节点处理失败")
    
    print()


def test_report_node_processing():
    """测试 report 节点的数据处理"""
    print("=== 测试 Report 节点处理 ===")
    
    # 模拟 report 节点的 chunk 数据
    report_chunk = {
        "current_step": "report",
        "stream_content": "根据调查结果，现将张三的密接人员情况报告如下：",
        "stream": True
    }
    
    # 处理数据
    result = stream_handler.process_node_chunk(
        node_name="report",
        chunk=report_chunk,
        thread_id="test-thread-123"
    )
    
    if result:
        print(f"✅ Report 节点处理成功:")
        print(f"   节点: {result.node}")
        print(f"   状态: {result.status}")
        print(f"   流式类型: {result.stream_type}")
        print(f"   内容: {result.content}")
    else:
        print("❌ Report 节点处理失败")
    
    print()


def test_custom_processor():
    """测试自定义处理器"""
    print("=== 测试自定义处理器 ===")
    
    # 创建一个自定义处理器
    class CustomNodeProcessor(DataStreamProcessor):
        def __init__(self):
            super().__init__(
                node_name="custom_node",
                data_key="custom_data",
                required_fields=["task_id", "progress"]
            )
    
    # 注册自定义处理器
    custom_processor = CustomNodeProcessor()
    stream_handler.register_custom_processor("custom_node", custom_processor)
    
    # 模拟自定义节点的数据
    custom_chunk = {
        "current_step": "custom_processing",
        "custom_data": {
            "task_id": "task-001",
            "progress": 75,
            "details": "正在处理自定义任务..."
        },
        "stream": True
    }
    
    # 处理数据
    result = stream_handler.process_node_chunk(
        node_name="custom_node",
        chunk=custom_chunk,
        thread_id="test-thread-123"
    )
    
    if result:
        print(f"✅ 自定义节点处理成功:")
        print(f"   节点: {result.node}")
        print(f"   状态: {result.status}")
        print(f"   流式类型: {result.stream_type}")
        print(f"   数据: {result.data}")
    else:
        print("❌ 自定义节点处理失败")
    
    print()


def test_invalid_data():
    """测试无效数据的处理"""
    print("=== 测试无效数据处理 ===")
    
    # 测试无效的 research 数据
    invalid_chunk = {
        "current_step": "research",
        "data": {
            "invalid_field": "这不是我们期望的字段"
        },
        "stream": True
    }
    
    result = stream_handler.process_node_chunk(
        node_name="research",
        chunk=invalid_chunk,
        thread_id="test-thread-123"
    )
    
    if result is None:
        print("✅ 无效数据正确被忽略")
    else:
        print("❌ 无效数据被错误处理")
    
    print()


def test_stream_data_creation():
    """测试 StreamData 对象创建"""
    print("=== 测试 StreamData 对象创建 ===")
    
    try:
        # 测试基本的 StreamData 创建
        stream_data = StreamData(
            node="test_node",
            status="started",
            thread_id="test-thread",
            timestamp="2024-01-01 12:00:00"
        )
        print("✅ 基本 StreamData 创建成功")
        
        # 测试带有流式数据的 StreamData 创建
        stream_data_with_content = StreamData(
            node="test_node",
            status="processing",
            thread_id="test-thread",
            timestamp="2024-01-01 12:00:00",
            stream_type="text",
            content="测试内容"
        )
        print("✅ 带内容的 StreamData 创建成功")
        
        # 测试带有数据的 StreamData 创建
        stream_data_with_data = StreamData(
            node="test_node",
            status="processing",
            thread_id="test-thread",
            timestamp="2024-01-01 12:00:00",
            stream_type="data",
            data={"key": "value"}
        )
        print("✅ 带数据的 StreamData 创建成功")
        
    except Exception as e:
        print(f"❌ StreamData 创建失败: {e}")
    
    print()


def main():
    """运行所有测试"""
    print("开始测试流式处理器...")
    print("=" * 50)
    
    test_stream_data_creation()
    test_research_node_processing()
    test_report_node_processing()
    test_custom_processor()
    test_invalid_data()
    
    print("=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
