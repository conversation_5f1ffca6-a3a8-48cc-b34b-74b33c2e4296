#!/usr/bin/env python3
"""示例：如何将现有节点迁移到新的统一输出格式"""

import asyncio
from typing import Dict, Any, AsyncGenerator
from app.agents.node_utils import NodeOutputHelper, create_node_helper


# 原始的 research 节点输出方式（旧格式）
async def old_research_node_output(info: str, final_result: str):
    """旧格式的 research 节点输出"""
    return {
        "current_step": "research",
        "data": {
            "demand_info": info,
            "final_result": final_result
        },
        "stream": True
    }


# 新格式的 research 节点输出方式
async def new_research_node_output(info: str, final_result: str):
    """新格式的 research 节点输出 - 使用统一的 data 格式"""
    helper = create_node_helper("research")
    
    # 方式1：使用 stream_data 输出结构化数据
    return helper.stream_data({
        "demand_info": info,
        "final_result": final_result
    })


# 原始的 report 节点输出方式（旧格式）
async def old_report_node_output(content: str):
    """旧格式的 report 节点输出"""
    return {
        "current_step": "report",
        "stream_content": content,
        "stream": True
    }


# 新格式的 report 节点输出方式
async def new_report_node_output(content: str):
    """新格式的 report 节点输出 - 使用统一的 data 格式"""
    helper = create_node_helper("report")
    
    # 方式1：使用 stream_text 输出文本内容
    return helper.stream_text(content)


# 完整的节点示例 - 展示如何在实际节点中使用
async def example_research_node_new_format(state) -> AsyncGenerator[Dict[str, Any], None]:
    """示例：使用新格式的 research 节点"""
    helper = create_node_helper("research")
    
    # 获取需要研究的信息列表
    information_needed = ["张三的密接人员", "张三的行程轨迹", "张三的健康状态"]
    
    # 输出开始进度
    yield helper.stream_progress(0.0, "开始研究任务...")
    
    total_tasks = len(information_needed)
    for i, info in enumerate(information_needed):
        # 输出当前处理的信息
        yield helper.stream_progress(
            progress=(i / total_tasks) * 100,
            message=f"正在处理: {info}"
        )
        
        # 模拟处理过程
        await asyncio.sleep(0.1)  # 模拟异步处理
        
        # 输出处理结果
        final_result = f"已完成 {info} 的调查分析"
        yield helper.stream_data({
            "demand_info": info,
            "final_result": final_result,
            "confidence": 0.95,
            "sources": ["数据库A", "数据库B"]
        })
    
    # 输出最终完成状态
    yield helper.stream_progress(100.0, "研究任务完成")
    
    # 输出最终结果（非流式）
    yield helper.final_output({
        "research_result": {
            "total_tasks": total_tasks,
            "completed_tasks": total_tasks,
            "status": "completed"
        },
        "messages": ["研究任务已完成"],
        "current_step": "research_completed"
    })


async def example_report_node_new_format(state) -> AsyncGenerator[Dict[str, Any], None]:
    """示例：使用新格式的 report 节点"""
    helper = create_node_helper("report")
    
    # 输出开始状态
    yield helper.stream_progress(0.0, "开始生成报告...")
    
    # 模拟流式生成报告内容
    report_chunks = [
        "# 调查报告\n\n",
        "## 概述\n",
        "根据调查结果，现将情况报告如下：\n\n",
        "## 详细信息\n",
        "1. 张三的密接人员包括：李四、王五、赵六\n",
        "2. 张三的行程轨迹已完成追踪\n",
        "3. 张三的健康状态良好\n\n",
        "## 结论\n",
        "调查工作已完成，所有信息已收集整理。"
    ]
    
    full_content = ""
    for i, chunk in enumerate(report_chunks):
        # 输出文本流
        yield helper.stream_text(chunk)
        full_content += chunk
        
        # 输出进度
        progress = ((i + 1) / len(report_chunks)) * 100
        yield helper.stream_progress(progress, f"报告生成进度: {progress:.1f}%")
        
        await asyncio.sleep(0.1)  # 模拟生成延迟
    
    # 输出最终结果
    yield helper.final_output({
        "report": full_content,
        "messages": ["报告生成完成"],
        "current_step": "report_completed",
        "is_complete": True
    })


# 对比函数 - 展示新旧格式的差异
async def compare_formats():
    """对比新旧格式的输出"""
    print("=== 格式对比 ===\n")
    
    # 旧格式输出
    old_output = await old_research_node_output("张三密接人员", "李四、王五、赵六")
    print("旧格式输出:")
    print(old_output)
    print()
    
    # 新格式输出
    new_output = await new_research_node_output("张三密接人员", "李四、王五、赵六")
    print("新格式输出:")
    print(new_output)
    print()
    
    print("=== 新格式的优势 ===")
    print("1. 统一的 data 结构，包含 event_type 字段")
    print("2. 标准化的时间戳")
    print("3. 更好的元数据支持")
    print("4. 工作流处理更简单，只需要处理 data 字段")
    print("5. 更容易扩展和维护")


# 工作流处理示例
def simplified_workflow_processing(chunk: Dict[str, Any]) -> bool:
    """简化的工作流处理 - 展示新格式如何简化处理逻辑"""
    
    # 新格式下，工作流只需要检查统一的结构
    if not isinstance(chunk, dict):
        return False
    
    # 检查是否是流式输出
    if chunk.get("stream") is True and "data" in chunk:
        data = chunk["data"]
        
        # 统一处理所有类型的流式数据
        event_type = data.get("event_type")
        node_name = data.get("node_name")
        
        print(f"处理节点 {node_name} 的 {event_type} 事件")
        
        # 根据事件类型进行相应处理
        if event_type == "text":
            print(f"文本内容: {data.get('content', '')}")
        elif event_type == "data":
            print(f"数据内容: {data.get('payload', {})}")
        elif event_type == "progress":
            print(f"进度: {data.get('progress', 0)}%")
        elif event_type == "result":
            print(f"结果: {data.get('result', {})}")
        
        return True
    
    return False


if __name__ == "__main__":
    # 运行对比示例
    asyncio.run(compare_formats())
    
    print("\n" + "="*50)
    print("运行新格式节点示例...")
    
    # 运行新格式的节点示例
    async def run_examples():
        print("\n=== Research 节点示例 ===")
        async for output in example_research_node_new_format({}):
            print(f"输出: {output}")
            # 使用简化的工作流处理
            simplified_workflow_processing(output)
            print("-" * 30)
    
    asyncio.run(run_examples())
