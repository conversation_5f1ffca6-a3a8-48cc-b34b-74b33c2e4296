# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# CORS Configuration
CORS_ORIGINS=["*"]

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Agent Configuration
MAX_ITERATIONS=10
TIMEOUT=30

# MCP Configuration
MCP_SERVER_URL=http://localhost:8080
MCP_TOOLS=["tool1", "tool2"]
MCP_TIMEOUT=30

# Agent Configuration
AUTO_ACCEPTED_PLAN=false