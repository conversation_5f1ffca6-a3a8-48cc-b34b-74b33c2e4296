"""API routes for the agent application."""

import logging
from typing import Optional
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
import json

from app.agents.workflow import agent_workflow
from app.models.schemas import ChatRequest, HealthResponse, ChatResponse, StreamData

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["agent"])


@router.get("/health")
async def health_check() -> HealthResponse:
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="0.1.0"
    )


@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest) -> ChatResponse:
    """Synchronous chat endpoint."""
    try:
        # For now, return a simple response
        # In a real implementation, this would use the agent workflow
        return ChatResponse(
            message=f"Echo: {request.query}",
            session_id=request.session_id or str(uuid4()),
            metadata={"mode": "sync"}
        )
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chat/stream")
async def chat_stream_endpoint(
    query: str = Query(..., description="User message"),
    session_id: Optional[str] = Query(None, description="Session ID")
) -> EventSourceResponse:
    """Streaming chat endpoint using Server-Sent Events."""
    try:
        # Create async generator for SSE
        async def event_generator():
            async for stream_data in agent_workflow.run(
                query=query
            ):
                # 直接发送 StreamData 对象的 JSON 数据，不包含 SSE 标识
                json_line = json.dumps(stream_data.model_dump(), ensure_ascii=False) + "\n"
                
                yield json_line
        
        return EventSourceResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
    except Exception as e:
        logger.error(f"Error in stream endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat/stream")
async def chat_stream_post_endpoint(request: ChatRequest) -> EventSourceResponse:
    """Streaming chat endpoint using POST method."""
    try:
        async def event_generator():
            async for stream_data in agent_workflow.run(
                query = request.query,
                metadata=request.metadata,
                thread_id=request.thread_id,
                interrupt_feedback=request.interrupt_feedback,
                case_id=request.case_id
            ):
                # 直接发送 StreamData 对象的 JSON 数据，不包含 SSE 标识
                json_line = json.dumps(stream_data.model_dump(), ensure_ascii=False) + "\n"
                
                yield json_line
        
        return EventSourceResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
    except Exception as e:
        logger.error(f"Error in stream POST endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

