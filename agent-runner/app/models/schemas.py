"""Pydantic models for API requests and responses."""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    
    query: str = Field(..., description="User message")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversation continuity")
    interrupt_feedback: Optional[Dict] = Field(None, description="Interrupt feedback")
    case_id: Optional[str] = Field(None, description="Case ID for conversation continuity")


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    
    message: str = Field(..., description="Agent response")
    session_id: str = Field(..., description="Session ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class SSEMessage(BaseModel):
    """Server-Sent Events message model."""
    
    event: str = Field(..., description="Event type")
    data: Dict[str, Any] = Field(..., description="Event data")
    id: Optional[str] = Field(None, description="Event ID")
    retry: Optional[int] = Field(None, description="Retry interval in milliseconds")


class AgentStatus(BaseModel):
    """Agent execution status."""
    
    status: str = Field(..., description="Current status")
    step: Optional[str] = Field(None, description="Current step")
    progress: float = Field(0.0, description="Progress percentage")
    message: Optional[str] = Field(None, description="Status message")
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class StreamData(BaseModel):
    """Stream data model for agent workflow."""

    node: Optional[str] = Field(default=None, description="Node name")
    status: Optional[str] = Field(default=None, description="Status")  # started, processing, completed, error
    thread_id: Optional[str] = Field(default=None, description="Thread ID")
    timestamp: Optional[str] = Field(default=None, description="Timestamp")
    error: Optional[str] = Field(default=None, description="Error message")

    # 流式数据字段 - 所有字段都是可选的以保持向后兼容
    stream_type: Optional[str] = Field(default=None, description="Stream data type: text, data, result")
    content: Optional[str] = Field(default=None, description="Stream content for text type")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Stream data for data type")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Final result for result type")

    # 允许任意其他字段以保持向后兼容
    class Config:
        extra = "allow"


class NodeStreamEvent(BaseModel):
    """标准化的节点流式事件模型"""

    event_type: str = Field(..., description="Event type: start, stream, end, error")
    node_name: str = Field(..., description="Node name")
    stream_type: Optional[str] = Field(None, description="Stream type: text, data, result")
    content: Optional[str] = Field(None, description="Stream content")
    data: Optional[Dict[str, Any]] = Field(None, description="Stream data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class HealthResponse(BaseModel):
    """Health check response."""
    
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(default_factory=datetime.utcnow)