"""LangGraph 工作流节点实现，包括检索、规划、反馈、研究和报告节点。"""

import logging
import json
from typing import Dict, Any, List, Optional, Literal, AsyncGenerator

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda
from langgraph.types import Command, interrupt
from pydantic import SecretStr
from langgraph.prebuilt import create_react_agent
# 使用 LangChain 的 Jinja2 模板注入方式
from app.config.prompt_config import prompt_manager
from app.config.settings import settings
from .types import AgentState
from .mcp_tools import search_apis, load_mcp_tools, execute_api
from app.config.prompt_config import PROMPT_CONFIG
from app.utils.json_utils import repair_json_output
  # 使用 Jinja2 手动渲染模板，然后包装成消息对象
from jinja2 import Template
from langgraph.graph import END

logger = logging.getLogger(__name__)

# 初始化 LLM
api_key = SecretStr(settings.openai_api_key) if settings.openai_api_key else None
llm = ChatOpenAI(
    model=settings.openai_model,
    api_key=api_key,
    base_url=settings.openai_base_url,
    temperature=0.3,
    streaming=False
)

# 全局 research agent 实例（延迟初始化）
_research_agent = None

async def get_research_agent():
    """获取全局的 research agent 实例，避免重复创建。"""
    global _research_agent
    
    if _research_agent is None:
        try:
            # 加载所有MCP工具
            await load_mcp_tools()
            from .mcp_tools import get_mcp_tools
            tools = await get_mcp_tools()
            
            if not tools:
                logger.warning("没有可用的MCP工具")
                tools = []
            
            # 创建全局 agent 实例，不设置 run_name 避免产生额外事件
            _research_agent = create_react_agent(llm, tools)
            logger.info("成功创建全局 research agent 实例")
        except Exception as e:
            logger.error(f"创建全局 research agent 失败: {str(e)}")
            # 创建一个没有工具的 agent 作为回退
            _research_agent = create_react_agent(llm, [])
    
    return _research_agent



async def comprehensive_retrieval_node(state: AgentState) -> Command[Literal[ "planning", "__end__"]]:
    """综合检索节点：查询向量库，返回相关 API 描述。"""
    logger.info(f"开始检索相关 api")
    
    # 当前步骤将在返回的 Command 中更新
    
    # 获取检索内容
    retrieval_content = state.get('query', '')
    # 如果没有检索内容，直接跳转到工作流结束
    if not retrieval_content:
        logger.info(f"没有检索内容，直接结束工作流")
        
        return Command(
            update={
                "messages": [AIMessage(content="用户问题未正确描述，请重新描述问题。")],
                "current_step": "completed",
                "is_complete": True
            },
            goto="planning"
        )
    # 消息将在返回的 Command 中更新
        
    # 意图识别判断是否需要检索api
    chat_history = []
    for message in state['messages'][:-1]:
        role = "user" if isinstance(message, HumanMessage) else "assistant"
        chat_history.append({"role": role, "content": message.content})
    

    
    # 获取模板字符串
    system_template = prompt_manager.get_prompt("retrieval", "system")
    user_template = prompt_manager.get_prompt("retrieval", "user")
    
    # 渲染系统提示词
    system_rendered = Template(system_template).render(
        chat_history=chat_history,
        current_query=retrieval_content
    )
    
    # 渲染用户提示词
    user_rendered = Template(user_template).render(
        chat_history=chat_history,
        current_query=retrieval_content
    )
    
    # 创建消息列表
    messages = [
        AIMessage(content=system_rendered),
        HumanMessage(content=user_rendered)
    ]
    
    # 调用 LLM 进行判断
    response = await llm.ainvoke(messages)
    
    # 解析响应，判断是否需要检索
    try:
        repair_json_str = repair_json_output(response.content if isinstance(response.content, str) else str(response.content))
        result = json.loads(repair_json_str)
        if "not_retrieval" in result and result["not_retrieval"]:
            # 不需要检索，直接结束工作流
            logger.info(f"判断不需要进行 API 检索，则直接跳转到规划节点")
            return Command(
                update={
                    "messages": [
                        HumanMessage(content=retrieval_content),
                        AIMessage(content="根据对话历史和当前查询，判断不需要进行 API 检索。")
                    ],
                    "current_step": "planning",
                    "is_complete": False
                },
                goto="planning"
            )
        else:
            # 需要检索，获取检索内容
            retrieval_content = result.get("retrieval_content", retrieval_content)

            # 查询向量库，获取相关 API 描述
            retrieval_results = []  # 这里应该是向量库查询结果planning

            # 使用 @tool 装饰的函数需要使用 invoke 方法调用
            search_result = await search_apis.ainvoke({"query": retrieval_content, "topk": 10})

            # 解析 search_result，获取所有的 page_content 并用换行分割
            try:
                search_data = json.loads(search_result)
            except json.JSONDecodeError:
                search_data = []


            # 提取所有的 page_content
            page_contents = []
            for item in search_data:
                if isinstance(item, dict) and "page_content" in item:
                    page_contents.append(item["page_content"])

            # 用换行符连接所有的 page_content 作为检索结果
            retrieval_results  = "\n".join(page_contents)

            return Command(
                update={
                    "messages": [
                        HumanMessage(content=retrieval_content),
                        AIMessage(content=f"已完成 API 检索，找到 {len(page_contents)} 个相关 API。")
                    ],
                    "retrieval_results": retrieval_results,
                    "current_step": "planning"
                },
                goto="planning"
            )
    except json.JSONDecodeError:
        # 解析失败，默认进行检索
        logger.error(f"解析 LLM 响应失败，默认进行检索")

        # 查询向量库，获取相关 API 描述
        retrieval_results = ""  # 这里应该是向量库查询结果

        return Command(
            update={
                "messages": [
                    HumanMessage(content=retrieval_content),
                    AIMessage(content="LLM 响应解析失败，默认进行 API 检索。")
                ],
                "retrieval_results": retrieval_results,
                "current_step": "planning"
            },
            goto="planning")


async def planning_node(state: AgentState) -> Dict[str, Any]:
    """规划节点：结合检索内容进行规划，不暴露 API 信息。"""
    logger.info(f"执行规划节点 ")
    
    # 当前步骤将在返回的字典中更新
    
    # 获取用户查询和检索结果
    user_query = state.get('query')
    retrieval_results = state.get('retrieval_results', [])
    
    # 获取提示词模板
    
    # 获取模板字符串
    system_template = prompt_manager.get_prompt("planning", "system")
    user_template = prompt_manager.get_prompt("planning", "user")
    # 获取问答历史
    chat_history = []
    for message in state['messages'][:-1]:
        role = "user" if isinstance(message, HumanMessage) else "assistant"
        chat_history.append({"role": role, "content": message.content})
       # 渲染系统提示词
    system_rendered = Template(system_template).render(
        chat_history=chat_history,
        query=user_query,
        retrieval_results = retrieval_results
    )
    
    # 渲染用户提示词
    user_rendered = Template(user_template).render(
        chat_history=chat_history,
        query=user_query, 
        retrieval_results = retrieval_results    
    ) 
    
    
    # 调用 LLM 进行规划
    response = await llm.ainvoke([
        SystemMessage(content=system_rendered),
        HumanMessage(content=user_rendered)
    ])
    
    # 解析规划结果
    try:
        repair_json_str = repair_json_output(response.content if isinstance(response.content, str) else str(response.content))
        plan_result = json.loads(repair_json_str)
    except json.JSONDecodeError:
        # 如果解析失败，使用默认格式
        plan_result = {
            "information_needed": ["根据用户查询获取相关信息"],
            "reasoning": "基于用户需求和可用资源制定信息获取计划"
        }
    
    return {
        "plan_result": plan_result,
        "current_step": "planning"
    }


async def human_feedback_node(state: AgentState) ->  Command[Literal[ "research","__end__"]]:
    """人类反馈节点：让用户判断是否正确或需要修改。"""
    logger.info(f"执行用户反馈节点")
    
    # 当前步骤将在返回的 Command 中更新
    
    # 获取规划结果
    plan_result = state.get('plan_result', {})
    
    # 安全获取 auto_accepted_plan，默认为 False
    auto_accepted_plan = state.get('auto_accepted_plan', False)
    
    if auto_accepted_plan:
        return Command(
        update={
            "plan_result": plan_result,
            "current_step": "human_feedback"
        },
        goto="research"
    )
    # 在实际应用中，这里应该等待用户输入
    feedback = interrupt("请提供反馈：{\"action\": \"ACCEPTED\"} 或 {\"action\": \"EDIT_PLAN\", \"information_needed\": [\"新信息1\", \"新信息2\"]} 或 {\"action\": \"REJECTED\", \"reason\": \"拒绝原因\"}")
    
    try:
        # 解析JSON格式的反馈
        if feedback and isinstance(feedback, dict):
            feedback_data = feedback
        elif feedback and isinstance(feedback, str):
            feedback_data = json.loads(feedback)
        else:
            feedback_data = {}
        action = feedback_data.get("action", "").upper()
        
        if action == "EDIT_PLAN":
            new_info_list = feedback_data.get("information_needed", [])
            if new_info_list and isinstance(new_info_list, list):
                # 确保plan_result不为None
                if plan_result is None:
                    plan_result = {}
                plan_result["information_needed"] = new_info_list
                logger.info(f"用户修改了信息需求: {new_info_list}")
        
        elif action == "ACCEPTED":
            logger.info("信息需求已接受，开始执行计划..")          
        elif action == "REJECTED":
            logger.info("规划被拒绝，直接结束.")
            return Command(
                update={
                    "current_step": "human_feedback"
                },
                goto="__end__")

        else:
            raise ValueError(f"不支持的action: {action}")
            
    except (json.JSONDecodeError, KeyError) as e:
        logger.error(f"解析反馈JSON失败: {e}, 原始反馈: {feedback}")
        # 回退到默认接受
        return Command(
            update={
                "current_step": "research"
            },
            goto="__end__")
        
    return Command(
        update={
            "plan_result": plan_result,
            "current_step": "research"
        },
        goto="research"
    )
    


async def research_node(state: AgentState) -> AsyncGenerator[Dict[str, Any], None]:
    """Research 节点：使用create_react_agent和所有MCP工具获取信息。"""
    logger.info(f"执行 research 节点")
    
    # 当前步骤将在返回的字典中更新
    
    # 获取规划结果
    plan_result = state.get('plan_result', {})
    if plan_result is None:
        plan_result = {}
    information_needed = plan_result.get('information_needed', [])
    
    # 获取全局的 research agent 实例
    single_agent = await get_research_agent()
    
    # 获取工具信息用于提示词（用于模板渲染，不用于创建agent）
    from .mcp_tools import get_mcp_tools
    tools = await get_mcp_tools()
    tool_names = [tool.name for tool in tools] if tools else []
    
    # 执行React agent
    try:
        # 为每个信息需求单独调用agent，获取对应结果
        demand_results = {}
        
        for info in information_needed:
            if not info.strip():
                continue
                
            # 为单个信息需求构建提示词
            # 获取模板字符串
            system_template = prompt_manager.get_prompt("research", "system")
            user_template = prompt_manager.get_prompt("research", "user")
            
            # 渲染系统提示词
            single_system_prompt = Template(system_template).render(
                case_id=state['case_id'],
                tool_names=', '.join(tool_names) if tool_names else '暂无可用工具'
            )
            
            # 渲染用户提示词
            single_user_prompt = Template(user_template).render(
                info=info,
                user_query = state['query'],
            )
            
            # 使用预定义的 agent 实例，不再重复创建
                  
            single_messages = [
                SystemMessage(content=single_system_prompt),
                HumanMessage(content=single_user_prompt)
            ]
            
            try:
                single_response = await single_agent.ainvoke({"messages": single_messages})
                single_messages = single_response.get("messages", [])
                
                # 直接获取agent的最终输出结果
                final_result = None
                
                # React agent的最后一条消息通常是最终答案
                if single_messages:
                    last_message = single_messages[-1]
                    if isinstance(last_message, AIMessage) and last_message.content:
                        final_result = last_message.content
                
                # 如果没有结果，使用最后一条非工具调用的消息
                if not final_result:
                    for msg in reversed(single_messages):
                        if isinstance(msg, AIMessage) and msg.content and not hasattr(msg, 'tool_call_id'):
                            final_result = msg.content
                            break
                logger.info(f"agent 最后一条消息结果:{single_messages[-1]}")
                logger.info(f"final_result:{final_result}")
                demand_results[info] = {
                    "final_result": final_result or "未能获取结果"
                }
                yield {
                    "current_step": "research",
                    "data": {
                        "demand_info": info,
                        "final_result": final_result or "未能获取结果"
                    },
                    "stream": True
                }
                
            except Exception as e:
                logger.error(f"处理信息需求 '{info}' 时出错: {str(e)}")
                demand_results[info] = {
                    "error": str(e),
                    "tools_called": [],
                    "tool_results": {},
                    "final_result": None,
                    "execution_order": []
                }
                yield {
                    "current_step": "research",
                    "data": {
                        "demand_info": info,
                        "final_result": "未能获取结果",
                        "error": str(e),
                    },
                    "stream": True
                }
            logger.info(f"info 需求处理完成: data={demand_results[info]}")
        # 创建简洁的研究结果，只包含每个需求的execute_api结果
        research_result = demand_results
    
        
    except Exception as e:
        logger.error(f"React agent执行失败: {str(e)}")
        research_result = {
                     "error": "工具调用失败", 
        } 
      
    
    # 返回研究结果和更新当前步骤
    yield {
        "research_result": research_result,
        "current_step": "report"
    }


async def report_node(state: AgentState) -> AsyncGenerator[Dict[str, Any], None]:
    """Report 节点：根据 research 节点内容输出完整报告。"""
    logger.info(f"执行报告输出节点")
    
    # 当前步骤将在返回的字典中更新
    
    # 获取用户查询、规划结果和研究发现
    user_query = state.get('query', "")
    plan_result = state.get('plan_result', {})
    research_result = state.get('research_result', {})
    
    # 确保plan_result和research_result不为None
    if plan_result is None:
        plan_result = {}
    if research_result is None:
        research_result = {}
    
    # 获取模板字符串
    system_template = prompt_manager.get_prompt("report", "system")
    user_template = prompt_manager.get_prompt("report", "user")
    
    # 渲染用户提示词
    user_rendered = Template(user_template).render(
        user_query=user_query,
        research_result=research_result
    )

    # 渲染系统提示词
    system_rendered = Template(system_template).render()

    # 调用 LLM 生成报告（流式输出）
    report_content = ""

    # 使用 astream 实现异步流式输出
    async for chunk in llm.astream([
        SystemMessage(content=system_rendered),
        HumanMessage(content=user_rendered)
    ]):
        if hasattr(chunk, 'content') and chunk.content:
            content = chunk.content
            if isinstance(content, str):
                report_content += content
                # 实时打印流式输出
                print(content, end='', flush=True)

                # 流式返回状态
                yield {
                    "current_step": "report",
                    "stream_content": content,
                    "stream": True
                }

    # 返回报告内容，并将报告作为 AIMessage 添加到消息历史
    yield {
        "messages": [AIMessage(content=report_content)],
        "report": report_content,
        "current_step": "completed",
        "is_complete": True
    }