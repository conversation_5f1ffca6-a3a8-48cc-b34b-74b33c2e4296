"""MCP 工具集成模块，使用 LangGraph 的 MultiServerMCPClient 和 SSE 协议接入 MCP 服务。"""

import logging
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

from langchain_core.tools import tool
from langchain_mcp_adapters.client import MultiServerMCPClient
from pydantic import BaseModel, Field, SecretStr

from app.config.settings import settings


logger = logging.getLogger(__name__)


class MCPToolRequest(BaseModel):
    """MCP 工具请求模型。"""
    
    tool_name: str = Field(..., description="工具名称")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="工具参数")
    session_id: Optional[str] = Field(None, description="会话ID")


class MCPToolResponse(BaseModel):
    """MCP 工具响应模型。"""
    
    tool_name: str = Field(..., description="工具名称")
    result: Any = Field(..., description="工具执行结果")
    success: bool = Field(..., description="执行是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间（秒）")


# MCP 工具映射
tool_map = {}


async def load_mcp_tools():
    """加载 MCP 工具。"""
    global tool_map
    
    # 从配置中获取 MCP 服务器 URL
    mcp_server_url = getattr(settings, 'mcp_server_url', 'http://127.0.0.1:8000/mcp')
    
    try:
        # 使用 MultiServerMCPClient 连接到 MCP 服务器
        client = MultiServerMCPClient({
            "mcp_server": {
                "url": mcp_server_url,
                "transport": "sse"
            }
        })
        # 获取所有工具
        tools = await client.get_tools()
        
        # 按名字索引工具
        tool_map = {t.name: t for t in tools}
        
        logger.info(f"Successfully loaded {len(tools)} tools from MCP server at {mcp_server_url}")
            
    except Exception as e:
        logger.error(f"Error loading MCP tools: {str(e)}")
        tool_map = {}


@tool
async def search_apis(query: str, topk: int = 5) -> str:
    """
    搜索相关 API，返回 API 描述信息。
    
    Args:
        query: 搜索查询字符串
        topk: 返回结果数量，默认为 5
        
    Returns:
        搜索结果的 JSON 字符串
    """
    try:
        # 确保工具已加载
        if not tool_map:
            await load_mcp_tools()
        
        # 获取 search_apis 工具
        search_tool = tool_map.get("search_apis")
        if not search_tool:
            return "search_apis 工具不可用"
        
        # 调用工具
        result = await search_tool.ainvoke({
            "query": query,
            "topk": topk
        })
        
        # return json.dumps(result, ensure_ascii=False)
        return result
    except Exception as e:
        logger.error(f"Error in search_apis tool: {str(e)}")
        return f"搜索出错: {str(e)}"


@tool
async def execute_api(url: str, method: str, params: List[Dict[str, Any]]) -> str:
    """
    执行 API 调用，获取 API 返回的数据。
    
    Args:
        url: API 的 URL 地址
        method: HTTP 方法，如 GET、POST、PUT、DELETE 等
        params: 参数列表，每个参数包含 name 和 value
        
    Returns:
        API 执行结果的 JSON 字符串
    """
    try:
        # 确保工具已加载
        if not tool_map:
            await load_mcp_tools()
        
        # 获取 execute_api 工具
        execute_tool = tool_map.get("execute_api")
        if not execute_tool:
            return "execute_api 工具不可用"
        
        # 调用工具
        result = await execute_tool.ainvoke({
            "url": url,
            "method": method,
            "params": params
        })
        
        # return json.dumps(result, ensure_ascii=False)
        return result
    except Exception as e:
        logger.error(f"Error in execute_api tool: {str(e)}")
        return f"API 执行出错: {str(e)}"


# 获取所有 MCP 工具
async def get_mcp_tools() -> List:
    """获取所有 MCP 工具。"""
    # 确保工具已加载
    if not tool_map:
        await load_mcp_tools()
    
    # 返回所有可用的MCP工具
    return list(tool_map.values())
