"""通用流式数据处理器"""

import logging
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from abc import ABC, abstractmethod

from app.models.schemas import StreamData, NodeStreamEvent

logger = logging.getLogger(__name__)


class BaseStreamProcessor(ABC):
    """流式数据处理器基类"""
    
    def __init__(self, node_name: str):
        self.node_name = node_name
    
    @abstractmethod
    def should_process(self, chunk: Dict[str, Any]) -> bool:
        """判断是否应该处理这个chunk"""
        pass
    
    @abstractmethod
    def extract_stream_data(self, chunk: Dict[str, Any]) -> Optional[NodeStreamEvent]:
        """从chunk中提取流式数据"""
        pass


class TextStreamProcessor(BaseStreamProcessor):
    """文本流式处理器 - 处理 stream_content 类型的数据"""
    
    def should_process(self, chunk: Dict[str, Any]) -> bool:
        return (
            isinstance(chunk, dict) and
            "stream_content" in chunk and
            chunk.get("stream") is True
        )
    
    def extract_stream_data(self, chunk: Dict[str, Any]) -> Optional[NodeStreamEvent]:
        content = chunk.get("stream_content", "")
        if content:
            return NodeStreamEvent(
                event_type="stream",
                node_name=self.node_name,
                stream_type="text",
                content=content,
                metadata={"current_step": chunk.get("current_step")}
            )
        return None


class DataStreamProcessor(BaseStreamProcessor):
    """数据流式处理器 - 处理结构化数据类型的流式输出"""
    
    def __init__(self, node_name: str, data_key: str = "data", required_fields: Optional[list] = None):
        super().__init__(node_name)
        self.data_key = data_key
        self.required_fields = required_fields or []
    
    def should_process(self, chunk: Dict[str, Any]) -> bool:
        if not isinstance(chunk, dict) or self.data_key not in chunk:
            return False
        
        data = chunk[self.data_key]
        if not isinstance(data, dict):
            return False
        
        # 检查必需字段
        return all(field in data for field in self.required_fields)
    
    def extract_stream_data(self, chunk: Dict[str, Any]) -> Optional[NodeStreamEvent]:
        data = chunk.get(self.data_key, {})
        return NodeStreamEvent(
            event_type="stream",
            node_name=self.node_name,
            stream_type="data",
            data=data,
            metadata={"current_step": chunk.get("current_step")}
        )


class ResultStreamProcessor(BaseStreamProcessor):
    """结果流式处理器 - 处理最终结果类型的数据"""
    
    def __init__(self, node_name: str, result_fields: Optional[list] = None):
        super().__init__(node_name)
        self.result_fields = result_fields or []
    
    def should_process(self, chunk: Dict[str, Any]) -> bool:
        if not isinstance(chunk, dict):
            return False
        
        # 检查是否包含结果字段
        return any(field in chunk for field in self.result_fields)
    
    def extract_stream_data(self, chunk: Dict[str, Any]) -> Optional[NodeStreamEvent]:
        result_data = {}
        for field in self.result_fields:
            if field in chunk:
                result_data[field] = chunk[field]
        
        if result_data:
            return NodeStreamEvent(
                event_type="stream",
                node_name=self.node_name,
                stream_type="result",
                data=result_data,
                metadata={"current_step": chunk.get("current_step")}
            )
        return None


class StreamProcessorRegistry:
    """流式处理器注册表"""
    
    def __init__(self):
        self._processors: Dict[str, list[BaseStreamProcessor]] = {}
        self._setup_default_processors()
    
    def _setup_default_processors(self):
        """设置默认的处理器"""
        # Research 节点处理器
        self.register_processor(
            "research",
            DataStreamProcessor(
                node_name="research",
                data_key="data",
                required_fields=["demand_info", "final_result"]
            )
        )
        
        # Report 节点处理器
        self.register_processor(
            "report",
            TextStreamProcessor(node_name="report")
        )
        
        # 通用结果处理器 - 处理所有节点的最终结果
        for node_name in ["retrieval", "planning", "human_feedback", "research", "report"]:
            self.register_processor(
                node_name,
                ResultStreamProcessor(
                    node_name=node_name,
                    result_fields=["messages", "report", "plan_result", "research_result", "retrieval_results"]
                )
            )
    
    def register_processor(self, node_name: str, processor: BaseStreamProcessor):
        """注册处理器"""
        if node_name not in self._processors:
            self._processors[node_name] = []
        self._processors[node_name].append(processor)
    
    def get_processors(self, node_name: str) -> list[BaseStreamProcessor]:
        """获取节点的处理器列表"""
        return self._processors.get(node_name, [])
    
    def process_chunk(self, node_name: str, chunk: Dict[str, Any]) -> Optional[NodeStreamEvent]:
        """处理chunk数据"""
        processors = self.get_processors(node_name)
        
        for processor in processors:
            if processor.should_process(chunk):
                try:
                    return processor.extract_stream_data(chunk)
                except Exception as e:
                    logger.error(f"处理器 {processor.__class__.__name__} 处理失败: {e}")
                    continue
        
        return None


class UniversalStreamHandler:
    """通用流式处理器"""
    
    def __init__(self):
        self.registry = StreamProcessorRegistry()
    
    def convert_to_stream_data(
        self,
        event: NodeStreamEvent,
        thread_id: str,
        timestamp: Optional[str] = None
    ) -> StreamData:
        """将 NodeStreamEvent 转换为 StreamData"""
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        stream_data = StreamData(
            node=event.node_name,
            status="processing",
            thread_id=thread_id,
            timestamp=timestamp,
            stream_type=event.stream_type
        )
        
        # 根据流式类型设置相应字段
        if event.stream_type == "text":
            stream_data.content = event.content
        elif event.stream_type == "data":
            stream_data.data = event.data
        elif event.stream_type == "result":
            stream_data.result = event.data
        
        # 添加元数据中的字段到 StreamData 对象
        if event.data and event.stream_type == "data":
            for key, value in event.data.items():
                setattr(stream_data, key, value)
        
        return stream_data
    
    def register_custom_processor(self, node_name: str, processor: BaseStreamProcessor):
        """注册自定义处理器"""
        self.registry.register_processor(node_name, processor)
    
    def process_node_chunk(
        self,
        node_name: str,
        chunk: Dict[str, Any],
        thread_id: str
    ) -> Optional[StreamData]:
        """处理节点的chunk数据"""
        event = self.registry.process_chunk(node_name, chunk)
        if event:
            return self.convert_to_stream_data(event, thread_id)
        return None


# 全局实例
stream_handler = UniversalStreamHandler()
