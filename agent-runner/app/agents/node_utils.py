"""节点工具函数 - 提供统一的输出格式"""

from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from app.agents.stream_processor import StandardNodeOutput, StreamEventType


class NodeOutputHelper:
    """节点输出辅助类"""
    
    def __init__(self, node_name: str):
        self.node_name = node_name
    
    def stream_text(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出文本流式数据"""
        return StandardNodeOutput.create_stream_output(
            node_name=self.node_name,
            event_type=StreamEventType.TEXT,
            content=content,
            metadata=metadata
        )
    
    def stream_data(self, data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出结构化数据"""
        return StandardNodeOutput.create_stream_output(
            node_name=self.node_name,
            event_type=StreamEventType.DATA,
            data=data,
            metadata=metadata
        )
    
    def stream_progress(self, progress: float, message: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出进度更新"""
        progress_data = {"progress": progress}
        if message:
            progress_data["message"] = message
        
        return StandardNodeOutput.create_stream_output(
            node_name=self.node_name,
            event_type=StreamEventType.PROGRESS,
            data=progress_data,
            metadata=metadata
        )
    
    def stream_result(self, result: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出最终结果（流式）"""
        return StandardNodeOutput.create_stream_output(
            node_name=self.node_name,
            event_type=StreamEventType.RESULT,
            data=result,
            metadata=metadata
        )
    
    def stream_error(self, error: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出错误信息"""
        return StandardNodeOutput.create_stream_output(
            node_name=self.node_name,
            event_type=StreamEventType.ERROR,
            content=error,
            metadata=metadata
        )
    
    def final_output(self, result: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """输出最终结果（非流式）"""
        return StandardNodeOutput.create_final_output(
            node_name=self.node_name,
            result=result,
            metadata=metadata
        )


# 便捷函数
def create_node_helper(node_name: str) -> NodeOutputHelper:
    """创建节点输出辅助器"""
    return NodeOutputHelper(node_name)


# 装饰器，自动为节点函数添加输出辅助器
def with_node_helper(node_name: str):
    """装饰器：为节点函数自动注入输出辅助器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 将输出辅助器作为第一个参数传入
            helper = NodeOutputHelper(node_name)
            return await func(helper, *args, **kwargs)
        return wrapper
    return decorator


# 示例用法函数
async def example_node_with_helper(state) -> AsyncGenerator[Dict[str, Any], None]:
    """示例：使用输出辅助器的节点"""
    helper = create_node_helper("example")
    
    # 输出进度
    yield helper.stream_progress(0.1, "开始处理...")
    
    # 输出文本流
    yield helper.stream_text("正在分析数据...")
    
    # 输出结构化数据
    yield helper.stream_data({
        "analysis_result": "数据分析完成",
        "confidence": 0.95
    })
    
    # 输出最终结果
    yield helper.final_output({
        "messages": ["处理完成"],
        "result": "success"
    })


# 为现有节点提供的迁移辅助函数
class NodeMigrationHelper:
    """节点迁移辅助类 - 帮助现有节点迁移到新格式"""
    
    @staticmethod
    def wrap_research_output(demand_info: str, final_result: str) -> Dict[str, Any]:
        """包装 research 节点的输出为新格式"""
        helper = NodeOutputHelper("research")
        return helper.stream_data({
            "demand_info": demand_info,
            "final_result": final_result
        })
    
    @staticmethod
    def wrap_report_text_output(content: str) -> Dict[str, Any]:
        """包装 report 节点的文本输出为新格式"""
        helper = NodeOutputHelper("report")
        return helper.stream_text(content)
    
    @staticmethod
    def wrap_planning_output(plan_result: Dict[str, Any]) -> Dict[str, Any]:
        """包装 planning 节点的输出为新格式"""
        helper = NodeOutputHelper("planning")
        return helper.stream_result(plan_result)
    
    @staticmethod
    def wrap_retrieval_output(retrieval_results: str) -> Dict[str, Any]:
        """包装 retrieval 节点的输出为新格式"""
        helper = NodeOutputHelper("retrieval")
        return helper.stream_result({"retrieval_results": retrieval_results})


# 全局辅助器实例，方便直接使用
research_helper = NodeOutputHelper("research")
report_helper = NodeOutputHelper("report")
planning_helper = NodeOutputHelper("planning")
retrieval_helper = NodeOutputHelper("retrieval")
human_feedback_helper = NodeOutputHelper("human_feedback")
