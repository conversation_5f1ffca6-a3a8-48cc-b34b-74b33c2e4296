from typing import List, Dict, Any, Optional
import uuid
from langchain_core.messages import AnyMessage
from langgraph.graph import MessagesState


class AgentState(MessagesState):
    """State for the agent workflow."""
    query: Optional[str]
    current_step: Optional[str]
    metadata: Optional[Dict[str, Any]]
    is_complete: Optional[bool]
    current_plan: Optional[List[str]]
    auto_accepted_plan: Optional[bool]
    case_id: Optional[str]
    retrieval_results: Optional[str]
    plan_result: Optional[Dict[str, Any]]
    human_feedback: Optional[Dict[str, Any]]
    research_result: Optional[Dict[str, Any]]
    report: Optional[str]
    case_id: Optional[str]
    stream_content: Optional[str]
    stream: Optional[bool]
    data: Optional[Dict[str, Any]]

def create_agent_state(
    messages: Optional[List[AnyMessage]] = None,
    current_step: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_complete: Optional[bool] = None,
    case_id: Optional[str] = None,
    retrieval_results: Optional[str] = None,
    query: Optional[str] = None,
) -> AgentState:
    """Create an AgentState instance with default values."""
    from app.config.settings import settings
    
    return AgentState(
        query=query,
        messages=messages or [],
        current_step=current_step or "start",
        metadata=metadata or {},
        is_complete=is_complete or False,
        current_plan=None,
        auto_accepted_plan=settings.auto_accepted_plan,
        case_id=case_id,
        retrieval_results=retrieval_results,
        plan_result=None,
        human_feedback=None,
        research_result=None,
        report=None,
        stream_content=None,
        stream=None,
        data=None
    )