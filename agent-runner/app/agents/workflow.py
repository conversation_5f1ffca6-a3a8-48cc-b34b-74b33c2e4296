"""LangGraph agent workflow implementation."""

import asyncio
import logging
from typing import As<PERSON><PERSON><PERSON>ator, Dict, Any, List, Optional
from datetime import datetime, timezone
import uuid

from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel, Field, SecretStr

from app.config.settings import settings
from app.models.schemas import AgentStatus, SSEMessage, StreamData
from app.agents.types import AgentState, create_agent_state
from app.agents.nodes import comprehensive_retrieval_node, human_feedback_node, planning_node, report_node, research_node
from langgraph.types import Command, Interrupt


logger = logging.getLogger(__name__)


class AgentWorkflow:
    """Main agent workflow using LangGraph."""
    
    def __init__(self):
        """Initialize the agent workflow."""
        api_key = SecretStr(settings.openai_api_key) if settings.openai_api_key else None
        self.llm = ChatOpenAI(
            model=settings.openai_model,
            api_key=api_key,
            base_url=settings.openai_base_url,
            temperature=0.7,
            streaming=True
        )
        # Initialize memory saver for thread-based persistence
        self.memory_saver = MemorySaver()
        self.graph = self._build_graph()
    
    def _build_graph(self):
        """Build the LangGraph workflow."""
        
        # Define the workflow
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("retrieval", comprehensive_retrieval_node)
        workflow.add_node("planning", planning_node)
        workflow.add_node("human_feedback", human_feedback_node)
        workflow.add_node("research", research_node)
        workflow.add_node("report", report_node)
        
        # Add edges
        workflow.add_edge("planning", "human_feedback")
        workflow.add_edge("research", "report")
        workflow.add_edge("report", END)
       
        
        # Set entry point
        workflow.set_entry_point("retrieval")
        
        # Compile with memory saver for thread-based persistence
        return workflow.compile(checkpointer=self.memory_saver)
        # return workflow.compile()

    def _is_interrupt_chunk(self, chunk: Dict[str, Any]) -> bool:
        """检查是否是中断类型的chunk"""
        return (
            isinstance(chunk, dict) and
            "__interrupt__" in chunk and
            chunk["__interrupt__"] is not None
        )

    def _process_interrupt_chunk(self, chunk: Dict[str, Any], node_name: str, thread_id: str) -> Optional[StreamData]:
        """处理中断类型的chunk数据"""
        try:
            interrupts: Interrupt = chunk.get("__interrupt__")

            # 根据你提供的格式：'chunk': {'__interrupt__': (Interrupt(value='请提供反馈：...'), ...)}
            # interrupts 可能是一个元组，其中第一个元素是 Interrupt 对象
            interrupts.value
            if isinstance(interrupts, (tuple, list)) and len(interrupts) > 0:
                interrupt_obj = interrupts[0]
                # 创建中断状态的 StreamData
                stream_data = StreamData(
                    node=node_name,
                    status="waiting_for_input",
                    thread_id=thread_id,
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    error=None
                )

                # 使用 __dict__ 设置额外字段（利用 extra="allow" 配置）
                stream_data.__dict__.update({
                    "stream_type": "interrupt",
                    "data": {
                        "interrupt_type": "user_feedback",
                        "message": str(interrupt_value),
                        "interrupt_value": str(interrupt_value),
                        "expected_action": "提供反馈",
                        "node_name": node_name
                    }
                })

                return stream_data

            # 如果不是预期的格式，尝试直接处理
            elif interrupts:
                logger.info(f"检测到非标准格式的中断事件: {interrupts}")

                stream_data = StreamData(
                    node=node_name,
                    status="waiting_for_input",
                    thread_id=thread_id,
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    error=None
                )

                # 使用 __dict__ 设置额外字段
                stream_data.__dict__.update({
                    "stream_type": "interrupt",
                    "data": {
                        "interrupt_type": "unknown",
                        "message": str(interrupts),
                        "interrupt_value": str(interrupts),
                        "expected_action": "提供反馈",
                        "node_name": node_name
                    }
                })

                return stream_data

        except Exception as e:
            logger.error(f"处理中断事件时出错: {e}")

        return None

    async def run(
        self,
        query: str,
        thread_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        interrupt_feedback: Optional[Dict] = None,
        case_id: Optional[str] = None
    ) -> AsyncGenerator[StreamData, None]:
        """Run the agent workflow with streaming."""
        
        # Create initial state
        state = create_agent_state(query=query,  metadata=metadata,case_id=case_id)
        if metadata:
            state['metadata'] = metadata
        
        # Create config for thread_id support
        config: RunnableConfig = {"configurable": {}}
        if thread_id is None:
            thread_id = str(uuid.uuid4())

        config["configurable"]["thread_id"] = thread_id
        config["recursion_limit"] = 30
        # 中断恢复输入
        if interrupt_feedback:
            state = Command(resume=interrupt_feedback)
        
        try:
            # 定义我们想要监控的节点列表
            valid_nodes = ["retrieval", "planning", "human_feedback", "research", "report", "LangGraph"]

            # Use astream_events with config for thread_id support to get more detailed events
            async for event in self.graph.astream_events(state, config=config, version="v2"):
                # 获取事件类型和数据
                event_type = event.get("event", "")
                data = event.get("data", {})
                metadata = data.get("metadata", {})
                tags = event.get("tags", [])
                # 只处理我们定义的节点
                node_name = event.get('name', '')
                
                if node_name not in valid_nodes:
                    continue
                 
                # 处理节点开始事件
                if event_type == "on_chain_start":
                    if len(tags) > 0 and tags[0] and tags[0].startswith("seq:"):
                        continue     
                    yield StreamData(
                        node=node_name,
                        status="started",
                        thread_id=thread_id,
                        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        error=None
                    )
                
                # 处理节点流式输出事件
                elif event_type == "on_chain_stream":
                    chunk = data.get("chunk", {})

                    # 优先检查是否是中断事件
                    if self._is_interrupt_chunk(chunk):
                        interrupt_data = self._process_interrupt_chunk(chunk, node_name, thread_id)
                        if interrupt_data:
                            yield interrupt_data
                            return  # 找到中断后返回
                    
                    # 检查是否为流式输出（不区分节点类型，只要 stream 为 true）
                    if isinstance(chunk, dict) and chunk.get("stream") is True and "data" in chunk:
                        # 创建 StreamData 对象，包含所有原始数据
                        stream_data = StreamData(
                            node=node_name,
                            status="processing",
                            thread_id=thread_id,
                            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            error=None
                        )
                        
                        # 将原始数据中的所有字段添加到 StreamData 对象中
                        setattr(stream_data, "data", chunk["data"])
                        
                        yield stream_data
                
                # 处理节点结束事件
                elif event_type == "on_chain_end":
                    if len(tags) > 0 and tags[0] and tags[0].startswith("seq:"):
                        continue
                    # 输出节点最终结果，将原始数据包装为 StreamData 对象
                    # 获取节点输出的原始数据
                    output_data = data.get("output", {})
                    
                    # 创建 StreamData 对象，包含所有原始数据
                    stream_data = StreamData(
                        node=node_name,
                        status="completed",
                        thread_id=thread_id,
                        timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        error=None
                    )
                    
                    # 安全处理不同类型的输出数据
                    try:
                        # 处理 Command 对象
                        if hasattr(output_data, 'update') and hasattr(output_data, 'goto'):
                            # 这是 Command 对象，提取 update 字典
                            command_data = getattr(output_data, 'update', {})
                            if isinstance(command_data, dict) and 'data' in command_data:
                                setattr(stream_data, 'data', command_data['data'])
                        # 处理普通字典数据
                        elif isinstance(output_data, dict) and 'data' in output_data:
                            setattr(stream_data, 'data', output_data['data'])
                        # 处理其他类型数据
                        elif isinstance(output_data, dict):
                            setattr(stream_data, 'data', output_data)
                    except Exception as e:
                        logger.warning(f"处理节点结束事件数据时出错: {e}")
                        # 保持 data 为空，避免中断整个流程
                    yield stream_data
                        
        except Exception as e:
            logger.error(f"Error in workflow execution: {str(e)}")
            yield StreamData(
                node="error",
                status="error",
                thread_id=thread_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                error=str(e)
            )
        

# Global agent instance
agent_workflow = AgentWorkflow()

graph = agent_workflow._build_graph()



if __name__ == "__main__":
    data = {
        "query": "张三密接人有哪些",
        "session_id": "123455",
        "metadata": {},
        "thread_id" : "cc7e47e4-1232-464c-81e2-5e8ac3db27dc",
        "interrupt_feedback": None,
        "case_id" : "123456"
        }
    
    # 运行工作流并打印 yield 的对象
    async def main():
        print("开始运行工作流...")
        async for message in agent_workflow.run(**data):
            print(f"收到消息: {message}")
            print(f"数据内容: {message.model_dump()}")
            print("-" * 50)
    
    # 运行异步主函数
    asyncio.run(main())
        
