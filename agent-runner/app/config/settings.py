"""Application configuration."""

import os
from typing import Optional

from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings."""
    
    # API Settings
    app_name: str = "FastAPI LangGraph Agent"
    app_version: str = "0.1.0"
    debug: bool = Field(default=False, validation_alias="DEBUG")
    
    # Server Settings
    host: str = Field(default="0.0.0.0", validation_alias="HOST")
    port: int = Field(default=8000, validation_alias="PORT")
    
    # CORS Settings
    cors_origins: list[str] = Field(
        default=["*"],
        validation_alias="CORS_ORIGINS"
    )
    
    # OpenAI Settings
    openai_api_key: Optional[str] = Field(default=None, validation_alias="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-3.5-turbo", validation_alias="OPENAI_MODEL")
    openai_base_url: str = Field(default="", validation_alias="OPENAI_BASE_URL")
    # LangGraph Settings
    max_iterations: int = Field(default=10, validation_alias="MAX_ITERATIONS")
    timeout: int = Field(default=30, validation_alias="TIMEOUT")
    
    # Logging
    log_level: str = Field(default="INFO", validation_alias="LOG_LEVEL")
    
    # MCP Settings
    mcp_server_url: str = Field(default="http://localhost:8080/mcp", validation_alias="MCP_SERVER_URL")
    mcp_tools: list[str] = Field(default=["tool1", "tool2"], validation_alias="MCP_TOOLS")
    mcp_timeout: int = Field(default=30, validation_alias="MCP_TIMEOUT")
    
    # Agent Settings
    auto_accepted_plan: bool = Field(default=False, validation_alias="AUTO_ACCEPTED_PLAN")
    
    # LangSmith Settings
    langsmith_tracing: bool = Field(default=True, validation_alias="LANGSMITH_TRACING")

    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()