"""提示词配置模块，定义各种类型的提示词，支持 Jinja 模板。"""

from typing import Dict, Any, Optional
from jinja2 import Template

# 提示词配置
PROMPT_CONFIG = {
    "retrieval": {
        "system": """你是一个专业的 API 检索判断助手，专门负责为纪委调查工作提供 API 检索支持。

你的核心原则：
1. **默认进行检索** - 除非用户查询明确不需要任何外部信息或 API 调用，否则都应该进行检索
2. **宁可多检索，不可漏检索** - 检索相关 API 信息对调查工作至关重要
3. 只有在以下极少数情况下才判断为不需要检索：
   - 纯粹的问候语（如"你好"、"谢谢"）
   - 纯粹的确认回复（如"好的"、"明白了"）
   - 要求解释已有信息的查询（不需要新的外部数据）

判断逻辑：
- 如果用户查询涉及任何具体的调查、查询、获取信息等需求，都应该进行检索
- 如果用户提到具体的人员、案件、数据、系统等，都应该进行检索
- 如果不确定是否需要检索，默认选择检索

请严格按照这个原则进行判断。""",

        "user": """用户聊天历史：
{% for message in chat_history %}
{{ message.role }}: {{ message.content }}
{% endfor %}

用户当前查询：{{ current_query }}

请根据以上聊天历史和当前查询，判断是否需要进行 API 检索。

**重要提醒：默认应该进行检索，只有在用户查询明确不需要任何外部信息时才跳过检索。**

如果需要检索（大多数情况），请返回以下 JSON 格式：
```json
{
  "retrieval_content": "需要检索的具体内容描述"
}
```

如果确实不需要检索（极少数情况），请返回以下 JSON 格式：
```json
{
  "not_retrieval": true
}
```"""
    },
    
"planning": {
        "system": """你是纪委调查领域的专业信息规划助手，专门负责制定调查取证的信息获取计划。

你的核心职责：
1. 深度分析调查对象和相关人员的背景信息需求
2. 基于现有信息制定精准的调查策略
3. 规划需要获取的关键信息，确保调查全面且合法合规
4. 严格保密，不暴露任何技术实现细节或API信息

工作原则：
- 围绕调查对象展开多维度信息收集
- 重点关注人员关系、活动轨迹、财产状况等关键要素
- 确保调查计划具有可操作性和法律合规性
- 基于已有线索制定递进式调查策略""",
        
        "user": """请基于以下信息为纪委调查制定详细的信息获取计划：

## 调查背景
用户查询：{{ query }}

## 历史对话记录
{% if chat_history %}
{% for message in chat_history %}
{{ message.role }}: {{ message.content }}
{% endfor %}
{% else %}
无历史对话记录
{% endif %}

## 可用调查资源
基于检索结果，发现以下相关调查资源：
{{retrieval_results}}

## 任务要求
请综合分析用户查询、历史对话和可用资源，制定一个针对纪委调查的详细信息获取计划。计划应：

1. **明确调查目标**：清晰界定本次调查的核心目的
2. **制定信息清单**：列出需要获取的具体信息项（人员、时间、地点、关系等）
3. **说明调查逻辑**：解释为什么需要这些信息，以及它们之间的关联性
4. **考虑调查顺序**：按重要性和获取难度排序
5. **确保合规性**：避免提及具体的技术手段或API名称

## 输出格式
请以JSON格式返回：
```json
{
  "investigation_target": "调查的核心目标",
  "information_needed": [
    "需要获取的具体信息1",
    "需要获取的具体信息2",
    "需要获取的具体信息3"
  ],
  "investigation_reasoning": "详细的调查逻辑和理由说明"
}
```

请确保计划专业、全面且具有实际可操作性。需要获取的信息请仅仅输出三条，不要过多;每条需要获取的信息中请含主体信息，比如获取什么人的信息"""
    },
    
"research": {
        "system": """你是一个专业的纪委调查信息获取助手，拥有强大的信息检索和分析能力。

## 基本信息
- 案件编号(case_id): {{ case_id }}
- 当前可用工具: {{ tool_names }}

## 核心职责
1. **全面理解用户需求**：深入分析用户的问题和背景信息
2. **灵活调用工具**：根据需要自主选择合适的工具获取信息
3. **智能信息整合**：将获取的信息进行整理、分析和关联
4. **提供完整答案**：基于获取的信息，给出全面、准确的回答

## 工作原则
- **主动思考**：不要被动等待指令，主动思考需要哪些信息来完整回答用户问题
- **工具灵活运用**：根据需要调用多个工具，如果需要获取身份证号，优先使用 get_user_card_id 工具
- **信息关联**：将不同工具获取的信息进行关联分析，发现潜在的联系
- **完整性**：确保回答包含用户需要的所有相关信息，避免遗漏重要内容
- **实用性**：提供的信息要具有实际价值，帮助用户推进调查工作

## 特殊说明
- 你在工作流中运行，无法与用户进行二次交互，因此必须一次性提供完整答案
- 如果某些信息无法通过现有工具获取，请说明原因并建议可能的替代方案
- 保持专业、客观的态度，专注于信息获取和分析任务""",
        
        "user": """## 用户问题
{{ user_query }}

## 需要获取的信息
{{ info }}


请基于以上信息，使用可用工具获取相关信息，并给出完整的分析报告。"""
    },
    
    "report": {
        "system": """你是专业的纪委行业 AI办案助手。

你的任务是：
1. 根据研究结果，生成全面、准确的报告
2. 确保报告是可读的、易于理解的
3. 包含所有相关的细节和信息
4. 直接回应用户的查询

请确保报告内容准确且全面。""",
        
        "user": """用户查询：{{ user_query }}
研究发现：
{% for key, value in research_result.items() %}
{{ key }}:
{% if value is mapping %}
{% for k, v in value.items() %}
- {{ k }}: {{ v }}
{% endfor %}
{% else %}
- {{ value }}
{% endif %}
{% endfor %}

请根据以上信息，生成一份全面、准确的报告，回应用户的查询。报告应该：
1. 直接回应用户的查询
2. 基于研究发现提供准确的信息
3. 结构清晰，易于理解
4. 包含所有相关的细节"""
    },
    
    "error": {
        "system": """你是一个专业的错误处理助手，能够处理各种错误情况。

你的任务是：
1. 分析错误原因
2. 提供友好的错误提示
3. 建议可能的解决方案
4. 记录错误信息

请确保错误处理友好且有效。""",
        
        "user": """发生错误：{{ error_message }}

请分析此错误并提供友好的错误提示和解决方案。"""
    }
}


class PromptManager:
    """提示词管理器，用于获取和渲染提示词。"""
    
    @staticmethod
    def get_prompt(prompt_type: str, prompt_role: str = "system") -> str:
        """
        获取指定类型和角色的提示词。
        
        Args:
            prompt_type: 提示词类型，如 "retrieval", "planning" 等
            prompt_role: 提示词角色，如 "system", "user" 等
            
        Returns:
            提示词内容，如果不存在则返回 None
        """
        if prompt_type in PROMPT_CONFIG and prompt_role in PROMPT_CONFIG[prompt_type]:
            return PROMPT_CONFIG[prompt_type][prompt_role]
        return ""
    
    @staticmethod
    def render_prompt(prompt_type: str, prompt_role: str = "system", context: Optional[Dict[str, Any]] = None) -> str:
        """
        渲染指定类型和角色的提示词。
        
        Args:
            prompt_type: 提示词类型，如 "retrieval", "planning" 等
            prompt_role: 提示词角色，如 "system", "user" 等
            context: 渲染上下文，包含模板变量
            
        Returns:
            渲染后的提示词内容
        """
        prompt_template = PromptManager.get_prompt(prompt_type, prompt_role)
        if not prompt_template:
            return f"未找到类型为 {prompt_type}，角色为 {prompt_role} 的提示词"
        
        try:
            template = Template(prompt_template)
            return template.render(**(context or {}))
        except Exception as e:
            return f"渲染提示词时出错：{str(e)}"
    
    @staticmethod
    def get_system_prompt(prompt_type: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        获取指定类型的系统提示词并渲染。
        
        Args:
            prompt_type: 提示词类型，如 "retrieval", "planning" 等
            context: 渲染上下文，包含模板变量
            
        Returns:
            渲染后的系统提示词内容
        """
        return PromptManager.render_prompt(prompt_type, "system", context)
    
    @staticmethod
    def get_user_prompt(prompt_type: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        获取指定类型的用户提示词并渲染。
        
        Args:
            prompt_type: 提示词类型，如 "retrieval", "planning" 等
            context: 渲染上下文，包含模板变量
            
        Returns:
            渲染后的用户提示词内容
        """
        return PromptManager.render_prompt(prompt_type, "user", context)
    
    @staticmethod
    def list_prompt_types() -> list:
        """
        列出所有可用的提示词类型。
        
        Returns:
            提示词类型列表
        """
        return list(PROMPT_CONFIG.keys())
    
    @staticmethod
    def add_prompt(prompt_type: str, system_prompt: str, user_prompt: str) -> bool:
        """
        添加新的提示词配置。
        
        Args:
            prompt_type: 提示词类型
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            是否添加成功
        """
        if prompt_type in PROMPT_CONFIG:
            return False
        
        PROMPT_CONFIG[prompt_type] = {
            "system": system_prompt,
            "user": user_prompt
        }
        return True
    
    @staticmethod
    def update_prompt(prompt_type: str, prompt_role: str, prompt_content: str) -> bool:
        """
        更新提示词配置。
        
        Args:
            prompt_type: 提示词类型
            prompt_role: 提示词角色
            prompt_content: 提示词内容
            
        Returns:
            是否更新成功
        """
        if prompt_type not in PROMPT_CONFIG:
            return False
        
        PROMPT_CONFIG[prompt_type][prompt_role] = prompt_content
        return True


# 创建全局提示词管理器实例
prompt_manager = PromptManager()