# FastAPI LangGraph Agent with SSE Support

基于 FastAPI 和 LangGraph 的智能体应用，支持通过 Server-Sent Events (SSE) 进行流式调用。

## 功能特性

- 🚀 **FastAPI** - 高性能异步 Web 框架
- 🧠 **LangGraph** - 基于图结构的智能体工作流
- 📡 **SSE 支持** - 实时流式响应
- 🔧 **可配置** - 支持环境变量配置
- 📊 **监控** - 内置健康检查和状态监控

## 项目结构

```
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 应用入口
│   ├── api/
│   │   ├── __init__.py
│   │   └── routes.py        # API 路由
│   ├── agents/
│   │   ├── __init__.py
│   │   └── workflow.py      # LangGraph 智能体工作流
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py      # 配置管理
│   ├── models/
│   │   ├── __init__.py
│   │   └── schemas.py       # Pydantic 模型
│   └── utils/
│       └── __init__.py
├── .env.example             # 环境变量示例
├── pyproject.toml          # 项目配置
├── run.py                  # 开发服务器启动脚本
└── README.md
```

## 快速开始

### 1. 安装依赖

```bash
# 使用 pip
pip install -e .

# 或使用 uv
uv pip install -e .
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，设置 OPENAI_API_KEY
```

### 3. 启动服务

```bash
# 使用启动脚本
python run.py

# 或使用 uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问 API

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health

## API 端点

### 同步聊天
```http
POST /api/v1/chat
Content-Type: application/json

{
  "message": "你好，智能体！",
  "session_id": "optional-session-id"
}
```

### 流式聊天 (GET)
```http
GET /api/v1/chat/stream?message=你好&session_id=optional-id
```

### 流式聊天 (POST)
```http
POST /api/v1/chat/stream
Content-Type: application/json

{
  "message": "你好，智能体！",
  "session_id": "optional-session-id"
}
```

## SSE 事件格式

流式响应使用 Server-Sent Events，事件类型包括：

- `status` - 状态更新
- `chunk` - 响应片段
- `complete` - 完成事件
- `error` - 错误事件

### 示例 SSE 响应

```json
event: status
data: {"status": "processing", "step": "generating", "progress": 50.0}

event: chunk
data: {"content": "这是"}

event: chunk
data: {"content": "一个"}

event: complete
data: {"session_id": "uuid", "message": "完整的响应", "metadata": {}}
```

## 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `HOST` | 0.0.0.0 | 服务器监听地址 |
| `PORT` | 8000 | 服务器端口 |
| `DEBUG` | false | 调试模式 |
| `OPENAI_API_KEY` | - | OpenAI API 密钥 |
| `OPENAI_MODEL` | gpt-3.5-turbo | OpenAI 模型名称 |
| `LOG_LEVEL` | INFO | 日志级别 |

## 开发

### 运行测试
```bash
pytest
```

### 代码格式化
```bash
black app/
ruff check app/
```

## 许可证

MIT License